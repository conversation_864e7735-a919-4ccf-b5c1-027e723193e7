### **Implement a Scheduled Push Notification System in Go**

**Objective:**
Build a robust, scheduled push notification system that integrates seamlessly with the existing Dinbora backend architecture. The system will be triggered by Google Cloud Scheduler, which calls a secure endpoint in our Go backend (using the Echo framework). The backend logic will query our MongoDB database to determine which notification (if any) to send to each user based on a predefined set of rules and priorities. The system extends the existing notification package, uses the established firebase package for FCM token management, and follows the layered architecture (Controller, Service, Repository) patterns already in place.

---

### **Phase 1: Project Setup & Prerequisites**

1.  **Environment Variables:**
    The following environment variables should already be configured in your system. Verify they are present:
    ```env
    # Database (already configured)
    DATABASE_URL="your_mongodb_connection_string"
    DATABASE_NAME="your_database_name"

    # Firebase (already configured)
    FIREBASE_CREDENTIALS_JSON="{...your_service_account_key_json...}"
    FIREBASE_ENCRYPTION_KEY="your-32-character-encryption-key"

    # Firebase Push Notification Authentication (add this)
    FIREBASE_PUSH_NOTIFICATION_API_KEY="your-secure-service-token-here"
    ```

2.  **Dependencies:**
    The required dependencies should already be installed in your project. Verify these are present in your `go.mod`:
    ```go
    // Core dependencies (already present)
    go.mongodb.org/mongo-driver/mongo
    github.com/labstack/echo/v4
    firebase.google.com/go/v4
    google.golang.org/api/option
    ```

---

### **Phase 2: Data Modeling (MongoDB & Go Structs)**

**Task:** Create the user activity state model that integrates with existing systems. FCM tokens are already managed by the firebase package, and user data is handled by the existing user model.

1.  **User Model:** The existing user model (`internal/model/user.go`) already handles user data. FCM tokens are managed separately in the firebase package.

2.  **Create User Activity State Model:** This model stores engagement and notification eligibility data.
    ```go
    // File: internal/model/notification/user_activity_state.go
    package notification

    import (
        "time"
        "go.mongodb.org/mongo-driver/bson/primitive"
    )

    // UserActivityState tracks user engagement data for push notification eligibility
    type UserActivityState struct {
        ObjectID                primitive.ObjectID `json:"-" bson:"_id,omitempty"`
        ID                      string             `json:"id,omitempty" bson:"-"`
        UserID                  string             `json:"userId" bson:"userId"`
        LastLoginAt             time.Time          `json:"lastLoginAt" bson:"lastLoginAt"`
        LastExpenseLoggedAt     time.Time          `json:"lastExpenseLoggedAt" bson:"lastExpenseLoggedAt"`
        LastProgressionActivity time.Time          `json:"lastProgressionActivity" bson:"lastProgressionActivity"`
        OnboardingState         OnboardingState    `json:"onboarding" bson:"onboarding"`
        NotificationState       NotificationState  `json:"notifications" bson:"notifications"`
        CreatedAt               time.Time          `json:"createdAt" bson:"createdAt"`
        UpdatedAt               time.Time          `json:"updatedAt" bson:"updatedAt"`
    }

    // OnboardingState tracks user onboarding progress
    type OnboardingState struct {
        StartedDiagnosisAt      time.Time `json:"startedDiagnosisAt" bson:"startedDiagnosisAt"`
        CompletedDiagnosisAt    time.Time `json:"completedDiagnosisAt" bson:"completedDiagnosisAt"`
        CreatedFirstGoalAt      time.Time `json:"createdFirstGoalAt" bson:"createdFirstGoalAt"`
        CreatedFirstBudgetAt    time.Time `json:"createdFirstBudgetAt" bson:"createdFirstBudgetAt"`
        CompletedFirstTrailAt   time.Time `json:"completedFirstTrailAt" bson:"completedFirstTrailAt"`
    }

    // NotificationState tracks sent notifications to prevent duplicates
    type NotificationState struct {
        SentUnique   map[string]time.Time `json:"sentUnique" bson:"sentUnique"`
        LastSentAt   time.Time            `json:"lastSentAt" bson:"lastSentAt"`
        TotalSent    int                  `json:"totalSent" bson:"totalSent"`
    }

    // PrepareCreate prepares the activity state for creation
    func (uas *UserActivityState) PrepareCreate() {
        uas.CreatedAt = time.Now()
        uas.UpdatedAt = uas.CreatedAt

        if uas.NotificationState.SentUnique == nil {
            uas.NotificationState.SentUnique = make(map[string]time.Time)
        }
    }

    // PrepareUpdate prepares the activity state for update
    func (uas *UserActivityState) PrepareUpdate() {
        uas.UpdatedAt = time.Now()
    }

    // Sanitize removes sensitive information and sets ID
    func (uas *UserActivityState) Sanitize() *UserActivityState {
        if !uas.ObjectID.IsZero() {
            uas.ID = uas.ObjectID.Hex()
        }
        return uas
    }
    ```

---

### **Phase 3: Repository Layer Implementation**

**Task:** Implement the repository layer following established patterns. This includes creating the notification repository and extending the user repository.

1.  **Add Collection Constant:** Add the new collection to the existing collections file.
    ```go
    // File: internal/repository/collections.go (add to existing constants)
    const (
        // ... existing collections ...
        NOTIFICATIONS_USER_ACTIVITY_STATES_COLLECTION = "notifications.user_activity_states"
        // ... rest of collections ...
    )
    ```

2.  **Notification Repository Interface:** Define the repository interface following established patterns.
    ```go
    // File: internal/repository/notification/repository.go
    package notification

    import (
        "context"
        "github.com/dsoplabs/dinbora-backend/internal/model/notification"
    )

    type Reader interface {
        FindByUserID(ctx context.Context, userID string) (*notification.UserActivityState, error)
        FindByUserIDs(ctx context.Context, userIDs []string) ([]*notification.UserActivityState, error)
        FindEligibleUsers(ctx context.Context, criteria map[string]interface{}) ([]*notification.UserActivityState, error)
    }

    type Writer interface {
        Create(ctx context.Context, state *notification.UserActivityState) error
        Update(ctx context.Context, state *notification.UserActivityState) error
        UpdateLastLogin(ctx context.Context, userID string) error
        UpdateLastExpenseLogged(ctx context.Context, userID string) error
        UpdateLastProgressionActivity(ctx context.Context, userID string) error
        UpdateNotificationSent(ctx context.Context, userID, notificationID string) error
        UpdateOnboardingEvent(ctx context.Context, userID, eventType string) error
        Upsert(ctx context.Context, state *notification.UserActivityState) error
        Delete(ctx context.Context, userID string) error
    }

    type Repository interface {
        Reader
        Writer
    }
    ```

3.  **MongoDB Repository Implementation:** Implement the notification repository following established patterns.
    ```go
    // File: internal/repository/notification/mongo.go
    package notification

    import (
        "context"
        "fmt"
        "log"
        "time"

        "github.com/dsoplabs/dinbora-backend/internal/errors"
        "github.com/dsoplabs/dinbora-backend/internal/model/notification"
        "github.com/dsoplabs/dinbora-backend/internal/repository"
        "go.mongodb.org/mongo-driver/bson"
        "go.mongodb.org/mongo-driver/mongo"
        "go.mongodb.org/mongo-driver/mongo/options"
    )

    type mongoDB struct {
        collection *mongo.Collection
    }

    // New creates a new notification repository instance following established pattern
    func New(db *mongo.Database) Repository {
        repo := &mongoDB{
            collection: db.Collection(repository.NOTIFICATIONS_USER_ACTIVITY_STATES_COLLECTION),
        }

        // Create indexes following established pattern
        ctx := context.Background()

        // Unique index on userID field
        _, err := repo.collection.Indexes().CreateOne(
            ctx,
            mongo.IndexModel{
                Keys:    bson.D{{Key: "userId", Value: 1}},
                Options: options.Index().SetUnique(true).SetName("userActivityState_userId"),
            },
        )
        if err != nil {
            log.Println("warning: failed to create index on user_activity_states.userId field:", err)
        }

        // Additional indexes for efficient queries
        _, err = repo.collection.Indexes().CreateOne(
            ctx,
            mongo.IndexModel{
                Keys: bson.D{{Key: "lastLoginAt", Value: 1}},
                Options: options.Index().SetName("userActivityState_lastLoginAt"),
            },
        )
        if err != nil {
            log.Println("warning: failed to create index on user_activity_states.lastLoginAt field:", err)
        }

        return repo
    }

    // Key repository methods (see full implementation in the complete guide)
    func (r *mongoDB) FindByUserIDs(ctx context.Context, userIDs []string) ([]*notification.UserActivityState, error) {
        filter := bson.M{"userId": bson.M{"$in": userIDs}}

        cursor, err := r.collection.Find(ctx, filter)
        if err != nil {
            return nil, errors.New(errors.Repository, "failed to find user activity states", errors.Internal, err)
        }
        defer cursor.Close(ctx)

        var states []*notification.UserActivityState
        for cursor.Next(ctx) {
            var state notification.UserActivityState
            if err := cursor.Decode(&state); err != nil {
                return nil, errors.New(errors.Repository, "failed to decode user activity state", errors.Internal, err)
            }
            states = append(states, state.Sanitize())
        }

        return states, nil
    }

    func (r *mongoDB) UpdateNotificationSent(ctx context.Context, userID, notificationID string) error {
        filter := bson.M{"userId": userID}
        update := bson.M{
            "$set": bson.M{
                fmt.Sprintf("notifications.sentUnique.%s", notificationID): time.Now(),
                "notifications.lastSentAt": time.Now(),
                "updatedAt":                time.Now(),
            },
            "$inc": bson.M{
                "notifications.totalSent": 1,
            },
        }

        _, err := r.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
        if err != nil {
            return errors.New(errors.Repository, "failed to update notification sent", errors.Internal, err)
        }

        return nil
    }
    ```

4.  **Extend User Repository:** Add method to find users with FCM tokens.
    ```go
    // File: internal/repository/user/repository.go (add to existing Reader interface)
    type Reader interface {
        // ... existing methods ...
        FindUsersWithFCMTokens(ctx context.Context) ([]*model.User, error)
    }
    ```

    ```go
    // File: internal/repository/user/mongo.go (implement new method)
    func (r *mongoDB) FindUsersWithFCMTokens(ctx context.Context) ([]*model.User, error) {
        // Join with firebase collection to find users with FCM tokens
        pipeline := []bson.M{
            {
                "$lookup": bson.M{
                    "from":         "fcm_tokens",
                    "localField":   "_id",
                    "foreignField": "userID",
                    "as":           "fcmTokens",
                },
            },
            {
                "$match": bson.M{
                    "fcmTokens": bson.M{"$ne": []interface{}{}},
                },
            },
            {
                "$project": bson.M{
                    "fcmTokens": 0, // Remove the joined field
                },
            },
        }

        cursor, err := r.collection.Aggregate(ctx, pipeline)
        if err != nil {
            return nil, errors.New(errors.Repository, "failed to find users with FCM tokens", errors.Internal, err)
        }
        defer cursor.Close(ctx)

        var users []*model.User
        for cursor.Next(ctx) {
            var user model.User
            if err := cursor.Decode(&user); err != nil {
                return nil, errors.New(errors.Repository, "failed to decode user", errors.Internal, err)
            }
            users = append(users, user.Sanitize())
        }

        return users, nil
    }
    ```

---

### **Phase 4: Service Layer Implementation**

**Task:** Extend the existing notification service to include push notification capabilities.

1.  **Extend Existing Notification Service:** Add push notification capabilities to the existing service.
    ```go
    // File: internal/service/notification/service.go (extend existing)

    // Add PushNotifier interface
    type PushNotifier interface {
        SendPushNotification(ctx context.Context, userID, title, body string, data map[string]string) error
        ProcessScheduledNotifications(ctx context.Context, notificationType string) error
    }

    // Extend existing Service struct
    type Service struct {
        // Existing providers
        BrevoNotifier Notifier
        GmailNotifier Notifier

        // New push notification provider
        PushNotifier PushNotifier
    }
    ```

2.  **Define Notification Rule Structure:** Create the rule engine for notifications.
    ```go
    // File: internal/service/notification/rules.go
    package notification

    import (
        "time"
        "github.com/dsoplabs/dinbora-backend/internal/model"
        "github.com/dsoplabs/dinbora-backend/internal/model/notification"
    )

    // NotificationRule defines a push notification rule
    type NotificationRule struct {
        ID          string
        Title       string
        Message     string
        Priority    int // Lower number = higher priority
        IsEligible  func(user *model.User, state *notification.UserActivityState) bool
    }
    ```

3.  **Implement Firebase Push Notifier:** Create the Firebase implementation.
    ```go
    // File: internal/service/notification/firebase_push.go
    package notification

    import (
        "context"
        "log"

        "firebase.google.com/go/v4"
        "firebase.google.com/go/v4/messaging"
        "github.com/dsoplabs/dinbora-backend/internal/errors"
        "github.com/dsoplabs/dinbora-backend/internal/service/firebase"
        "github.com/dsoplabs/dinbora-backend/internal/repository/notification"
        "github.com/dsoplabs/dinbora-backend/internal/repository/user"
    )

    // FirebasePushNotifier implements push notifications using Firebase Cloud Messaging
    type FirebasePushNotifier struct {
        firebaseApp         *firebase.App
        firebaseService     firebase.Service
        activityStateRepo   notification.Repository
        userRepo           user.Repository
    }

    // NewFirebasePushNotifier creates a new Firebase push notifier
    func NewFirebasePushNotifier(
        firebaseApp *firebase.App,
        firebaseService firebase.Service,
        activityStateRepo notification.Repository,
        userRepo user.Repository,
    ) PushNotifier {
        return &FirebasePushNotifier{
            firebaseApp:       firebaseApp,
            firebaseService:   firebaseService,
            activityStateRepo: activityStateRepo,
            userRepo:         userRepo,
        }
    }

    // SendPushNotification sends a push notification to a specific user
    func (f *FirebasePushNotifier) SendPushNotification(ctx context.Context, userID, title, body string, data map[string]string) error {
        // Get user's FCM token
        fcmToken, err := f.firebaseService.FindByUserID(ctx, userID)
        if err != nil {
            return errors.New(errors.Service, "failed to find user FCM token", errors.Internal, err)
        }

        // Get FCM client
        fcmClient, err := f.firebaseApp.Messaging(ctx)
        if err != nil {
            return errors.New(errors.Service, "failed to get Firebase messaging client", errors.Internal, err)
        }

        // Construct message following existing gamification service pattern
        message := &messaging.Message{
            Notification: &messaging.Notification{
                Title: title,
                Body:  body,
            },
            Data: data,

            // Platform-specific configuration (from existing gamification service)
            APNS: &messaging.APNSConfig{
                Headers: map[string]string{
                    "apns-priority": "10",
                },
                Payload: &messaging.APNSPayload{
                    Aps: &messaging.Aps{
                        ContentAvailable: true,
                        Sound:            "default",
                    },
                },
            },

            Android: &messaging.AndroidConfig{
                Priority: "high",
                Notification: &messaging.AndroidNotification{
                    ChannelID: "general_notifications",
                    Sound:     "default",
                },
            },

            Token: fcmToken.FCMToken,
        }

        // Send message with error handling for unregistered tokens
        messageID, err := fcmClient.Send(ctx, message)
        if err != nil {
            if messaging.IsUnregistered(err) {
                log.Printf("FCM token for user %s is unregistered. Deleting.", userID)
                f.firebaseService.Delete(ctx, userID)
                return nil
            }
            return errors.New(errors.Service, "failed to send push notification", errors.Internal, err)
        }

        log.Printf("Successfully sent push notification to user %s. FCM Message ID: %s", userID, messageID)
        return nil
    }
    ```

4.  **Implement Notification Rules:** Create the rule engine with example rules.
    ```go
    // File: internal/service/notification/rules.go (continued)

    // getNotificationRules returns rules based on notification type
    func (f *FirebasePushNotifier) getNotificationRules(notificationType string) []NotificationRule {
        switch notificationType {
        case "unique":
            return f.getUniqueRules()
        case "recurrent":
            return f.getRecurrentRules()
        default:
            return []NotificationRule{}
        }
    }

    // getUniqueRules returns one-time notification rules
    func (f *FirebasePushNotifier) getUniqueRules() []NotificationRule {
        return []NotificationRule{
            {
                ID:       "DIAGNOSTICO_FINANCEIRO",
                Title:    "Complete seu diagnóstico",
                Message:  "Ei, seu diagnóstico tá esperando! Bora descobrir seu perfil financeiro? 🚀",
                Priority: 1,
                IsEligible: func(user *model.User, state *notification.UserActivityState) bool {
                    // Check if already sent
                    if _, sent := state.NotificationState.SentUnique["DIAGNOSTICO_FINANCEIRO"]; sent {
                        return false
                    }

                    // Check conditions
                    return state.OnboardingState.CompletedDiagnosisAt.IsZero() &&
                           !state.OnboardingState.StartedDiagnosisAt.IsZero() &&
                           time.Since(state.OnboardingState.StartedDiagnosisAt) > 24*time.Hour
                },
            },
            {
                ID:       "PRIMEIRO_SONHO",
                Title:    "Crie seu primeiro sonho",
                Message:  "Que tal criar seu primeiro sonho financeiro? É o primeiro passo para realizá-lo! ✨",
                Priority: 2,
                IsEligible: func(user *model.User, state *notification.UserActivityState) bool {
                    if _, sent := state.NotificationState.SentUnique["PRIMEIRO_SONHO"]; sent {
                        return false
                    }

                    return !state.OnboardingState.CompletedDiagnosisAt.IsZero() &&
                           state.OnboardingState.CreatedFirstGoalAt.IsZero() &&
                           time.Since(state.OnboardingState.CompletedDiagnosisAt) > 24*time.Hour
                },
            },
            // Add more unique rules as needed...
        }
    }

    // getRecurrentRules returns recurring notification rules
    func (f *FirebasePushNotifier) getRecurrentRules() []NotificationRule {
        return []NotificationRule{
            {
                ID:       "INATIVIDADE",
                Title:    "Sentimos sua falta!",
                Message:  "Seus sonhos ainda estão aqui esperando você voltar! ✨",
                Priority: 1,
                IsEligible: func(user *model.User, state *notification.UserActivityState) bool {
                    return time.Since(state.LastLoginAt) > 7*24*time.Hour
                },
            },
            {
                ID:       "LEMBRETE_GASTOS",
                Title:    "Hora de registrar seus gastos",
                Message:  "Que tal registrar seus gastos de hoje? Manter o controle é fundamental! 💰",
                Priority: 2,
                IsEligible: func(user *model.User, state *notification.UserActivityState) bool {
                    return time.Since(state.LastExpenseLoggedAt) > 3*24*time.Hour
                },
            },
            // Add more recurrent rules as needed...
        }
    }

    // findBestNotificationRule finds the highest priority eligible rule
    func (f *FirebasePushNotifier) findBestNotificationRule(user *model.User, state *notification.UserActivityState, rules []NotificationRule) *NotificationRule {
        var bestRule *NotificationRule

        for _, rule := range rules {
            if rule.IsEligible(user, state) {
                if bestRule == nil || rule.Priority < bestRule.Priority {
                    bestRule = &rule
                }
            }
        }

        return bestRule
    }
    ```

---

### **Phase 5: Controller Layer Implementation**

**Task:** Create the notification controller following established Echo patterns.

1.  **Controller Interface and Structure:**
    ```go
    // File: internal/controller/notification/controller.go
    package notification

    import (
        "context"
        "net/http"

        "github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
        "github.com/dsoplabs/dinbora-backend/internal/service/notification"
        "github.com/labstack/echo/v4"
    )

    type Controller interface {
        // Routes
        RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

        // Push notification endpoints
        ProcessScheduledNotifications() echo.HandlerFunc
        SendTestNotification() echo.HandlerFunc
        GetUserActivityState() echo.HandlerFunc
    }

    type controller struct {
        NotificationService *notification.Service
    }

    func New(notificationService *notification.Service) Controller {
        return &controller{
            NotificationService: notificationService,
        }
    }

    // RegisterRoutes registers notification routes following established pattern
    func (nc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
        notificationGroup := currentGroup.Group("/notifications")

        // Scheduled notification processing (protected by service auth)
        notificationGroup.POST("/process", nc.ProcessScheduledNotifications(), middlewares.FirebasePushNotificationMiddleware())

        // Test endpoint for development (protected by admin auth)
        notificationGroup.POST("/test", nc.SendTestNotification(), middlewares.AuthGuard(), middlewares.UserContextMiddleware(), middlewares.AdminOnly())

        // User activity state endpoint (protected by user auth)
        notificationGroup.GET("/activity-state", nc.GetUserActivityState(), middlewares.AuthGuard(), middlewares.UserContextMiddleware())
    }
    ```

2.  **Controller Implementation:**
    ```go
    // File: internal/controller/notification/notification.go
    package notification

    import (
        "net/http"
        "time"

        "github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
        "github.com/dsoplabs/dinbora-backend/internal/errors"
        "github.com/labstack/echo/v4"
    )

    // ProcessScheduledNotifications handles scheduled notification processing requests
    func (nc *controller) ProcessScheduledNotifications() echo.HandlerFunc {
        return func(c echo.Context) error {
            ctx := c.Request().Context()

            // Parse request body
            var req ProcessNotificationsRequestDTO
            if err := c.Bind(&req); err != nil {
                return errors.NewValidationError(errors.Controller, "invalid request body", errors.KeyNotificationErrorInvalidRequest, err)
            }

            if err := c.Validate(&req); err != nil {
                return errors.NewValidationError(errors.Controller, "request validation failed", errors.KeyNotificationErrorValidationFailed, err)
            }

            // Validate notification type
            if req.Type != "unique" && req.Type != "recurrent" {
                return errors.NewValidationError(errors.Controller, "invalid notification type", errors.KeyNotificationErrorInvalidType, nil)
            }

            // Process notifications asynchronously to avoid timeout
            go func() {
                if nc.NotificationService.PushNotifier != nil {
                    err := nc.NotificationService.PushNotifier.ProcessScheduledNotifications(ctx, req.Type)
                    if err != nil {
                        c.Logger().Errorf("Failed to process scheduled notifications: %v", err)
                    }
                }
            }()

            return c.JSON(http.StatusAccepted, ProcessNotificationsResponseDTO{
                Status:  "accepted",
                Message: "Notification processing started",
                Type:    req.Type,
            })
        }
    }

    // SendTestNotification sends a test notification (admin only)
    func (nc *controller) SendTestNotification() echo.HandlerFunc {
        return func(c echo.Context) error {
            ctx := c.Request().Context()

            var req SendTestNotificationRequestDTO
            if err := c.Bind(&req); err != nil {
                return errors.NewValidationError(errors.Controller, "invalid request body", errors.KeyNotificationErrorInvalidRequest, err)
            }

            if err := c.Validate(&req); err != nil {
                return errors.NewValidationError(errors.Controller, "request validation failed", errors.KeyNotificationErrorValidationFailed, err)
            }

            // Send test notification
            if nc.NotificationService.PushNotifier != nil {
                data := map[string]string{
                    "type": "test",
                    "timestamp": time.Now().Format(time.RFC3339),
                }

                err := nc.NotificationService.PushNotifier.SendPushNotification(
                    ctx,
                    req.UserID,
                    req.Title,
                    req.Message,
                    data,
                )
                if err != nil {
                    return err
                }
            } else {
                return errors.New(errors.Controller, "push notification service not available", errors.ServiceUnavailable, nil)
            }

            return c.JSON(http.StatusOK, SendTestNotificationResponseDTO{
                Status:  "sent",
                Message: "Test notification sent successfully",
                UserID:  req.UserID,
            })
        }
    }
    ```

3.  **DTOs (Data Transfer Objects):**
    ```go
    // File: internal/controller/notification/dto.go
    package notification

    import "time"

    // ProcessNotificationsRequestDTO represents the request for processing scheduled notifications
    type ProcessNotificationsRequestDTO struct {
        Type string `json:"type" validate:"required,oneof=unique recurrent"`
    }

    // ProcessNotificationsResponseDTO represents the response for processing scheduled notifications
    type ProcessNotificationsResponseDTO struct {
        Status  string `json:"status"`
        Message string `json:"message"`
        Type    string `json:"type"`
    }

    // SendTestNotificationRequestDTO represents the request for sending a test notification
    type SendTestNotificationRequestDTO struct {
        UserID  string `json:"userId" validate:"required"`
        Title   string `json:"title" validate:"required,max=100"`
        Message string `json:"message" validate:"required,max=500"`
    }

    // SendTestNotificationResponseDTO represents the response for sending a test notification
    type SendTestNotificationResponseDTO struct {
        Status  string `json:"status"`
        Message string `json:"message"`
        UserID  string `json:"userId"`
    }
    ```

---

### **Phase 6: Infrastructure - Google Cloud Scheduler**

**Task:** Create scheduled jobs using Google Cloud Scheduler with service token authentication.

1.  **Generate Service Token:**
    ```bash
    # Generate a secure service token (32+ characters)
    openssl rand -base64 32
    ```
    Add this token to your environment variables as `FIREBASE_PUSH_NOTIFICATION_API_KEY`.

2.  **Create Scheduler Jobs:**
    ```bash
    # Create unique notifications job
    gcloud scheduler jobs create http process-unique-notifications \
        --schedule="0 11 * * *" \
        --uri="https://your-api-domain.com/v2/notifications/process" \
        --http-method=POST \
        --headers="Authorization=Bearer YOUR_SERVICE_TOKEN,Content-Type=application/json" \
        --message-body='{"type": "unique"}' \
        --time-zone="America/Sao_Paulo"

    # Create recurrent notifications job
    gcloud scheduler jobs create http process-recurrent-notifications \
        --schedule="0 20 * * *" \
        --uri="https://your-api-domain.com/v2/notifications/process" \
        --http-method=POST \
        --headers="Authorization=Bearer YOUR_SERVICE_TOKEN,Content-Type=application/json" \
        --message-body='{"type": "recurrent"}' \
        --time-zone="America/Sao_Paulo"
    ```

---

### **Phase 7: Integration and Wiring**

**Task:** Wire everything together in the existing application structure.

1.  **Update Repository Registry:**
    ```go
    // File: internal/api/repository/repository.go (add to existing)
    import (
        notificationRepo "github.com/dsoplabs/dinbora-backend/internal/repository/notification"
    )

    type RepositoryRegistry struct {
        // ... existing repositories ...
        Notification notificationRepo.Repository
    }

    func New(db *mongo.Database) *RepositoryRegistry {
        return &RepositoryRegistry{
            // ... existing repository initializations ...
            Notification: notificationRepo.New(db),
        }
    }
    ```

2.  **Update Service Registry:**
    ```go
    // File: internal/api/service/service.go (add to existing)
    type ServiceRegistry struct {
        // ... existing services ...
        Notification *notification.Service
    }

    func New(repositories *repository.RepositoryRegistry, firebaseApp *firebase.App) *ServiceRegistry {
        // ... existing service initializations ...

        notificationService := notification.New(
            firebaseApp,
            firebase.New(repositories.Firebase),
            repositories.Notification,
            repositories.User,
        )

        return &ServiceRegistry{
            // ... existing services ...
            Notification: notificationService,
        }
    }
    ```

3.  **Update Controller Registry:**
    ```go
    // File: internal/api/controller/controller.go (add to existing)
    import (
        "github.com/dsoplabs/dinbora-backend/internal/controller/notification"
    )

    type Controllers struct {
        // ... existing controllers ...
        Notification notification.Controller
    }

    func New(services *service.ServiceRegistry, repositories *repository.RepositoryRegistry) *Controllers {
        return &Controllers{
            // ... existing controller initializations ...
            Notification: notification.New(services.Notification),
        }
    }

    func (c *Controllers) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
        // ... existing route registrations ...
        c.Notification.RegisterRoutes(ctx, legacyGroup, currentGroup)
    }
    ```

4.  **Update Error Keys:**
    ```go
    // File: internal/errors/keys.go (add to existing error keys)
    const (
        // ... existing error keys ...

        // Notification error keys
        KeyNotificationErrorInvalidRequest     = "notification.error.invalidRequest"
        KeyNotificationErrorValidationFailed   = "notification.error.validationFailed"
        KeyNotificationErrorInvalidType        = "notification.error.invalidType"
        KeyNotificationErrorServiceUnavailable = "notification.error.serviceUnavailable"
        KeyNotificationErrorSendFailed         = "notification.error.sendFailed"
        KeyNotificationErrorNotFound           = "notification.error.notFound"
    )
    ```

---

### **Phase 8: Security Implementation**

**Task:** Implement security using existing authentication patterns.

Create a new `FirebasePushNotificationMiddleware` to protect the push notification endpoints, which validates the `FIREBASE_PUSH_NOTIFICATION_API_KEY` environment variable.

```go
// Scheduled notification processing (protected by service auth)
notificationGroup.POST("/process", nc.ProcessScheduledNotifications(), middlewares.FirebasePushNotificationMiddleware())
```

**Security Features:**
- Service token authentication using existing middleware
- Request validation with established error handling
- Async processing to prevent timeout attacks
- FCM token cleanup for unregistered devices
- Proper logging for monitoring and debugging

**Optional Enhanced Security:**
For additional security, you can implement IP allowlisting for Google Cloud Scheduler IP ranges by creating a custom middleware, but the service token authentication is sufficient for most use cases.

---

### **Phase 9: Integration with Existing Services**

**Task:** Hook into existing services to track user activity automatically.

1.  **Auth Service Integration:**
    ```go
    // File: internal/service/auth/service.go (add to existing Login method)
    func (s *service) Login(ctx context.Context, user *model.User) (*model.User, *token.Token, error) {
        // ... existing login logic ...

        // Update activity state
        if s.ActivityStateService != nil {
            go s.ActivityStateService.UpdateLastLogin(ctx, userData.ID)
        }

        return userData, token, nil
    }
    ```

2.  **Financial Sheet Service Integration:**
    ```go
    // File: internal/service/financialsheet/service.go (add to transaction creation)
    func (s *service) CreateTransaction(ctx context.Context, transaction *model.Transaction) error {
        // ... existing transaction logic ...

        // Update activity state
        if s.ActivityStateService != nil {
            go s.ActivityStateService.UpdateLastExpenseLogged(ctx, transaction.UserID)
        }

        return nil
    }
    ```

3.  **Progression Service Integration:**
    ```go
    // File: internal/service/progression/service.go (add to existing RecordProgress method)
    func (s *service) RecordProgress(ctx context.Context, userId string, body *progression.ProgressionBody) error {
        // ... existing progression logic ...

        // Update activity state
        if s.ActivityStateService != nil {
            go s.ActivityStateService.UpdateLastProgressionActivity(ctx, userId)
        }

        return nil
    }
    ```

---

### **Phase 10: Testing and Deployment**

**Task:** Test the implementation and deploy.

1.  **Testing Endpoints:**
    ```bash
    # Test scheduled notification processing (requires service token)
    curl -X POST https://your-api.com/v2/notifications/process \
      -H "Authorization: Bearer YOUR_SERVICE_TOKEN" \
      -H "Content-Type: application/json" \
      -d '{"type": "unique"}'

    # Test notification sending (requires admin auth)
    curl -X POST https://your-api.com/v2/notifications/test \
      -H "Authorization: Bearer USER_JWT_TOKEN" \
      -H "Content-Type: application/json" \
      -d '{"userId": "user_id", "title": "Test", "message": "Test message"}'
    ```

2.  **Monitoring and Logging:**
    - Monitor Google Cloud Scheduler job execution
    - Check application logs for notification processing
    - Monitor FCM delivery reports
    - Track user engagement metrics

3.  **Deployment Checklist:**
    - [ ] Environment variables configured
    - [ ] Service token generated and secured
    - [ ] Google Cloud Scheduler jobs created
    - [ ] Firebase credentials configured
    - [ ] Database indexes created
    - [ ] Error handling tested
    - [ ] Monitoring and alerting configured

---

### **Summary**

This implementation provides a robust, scheduled push notification system that:

- **Integrates seamlessly** with your existing Dinbora backend architecture
- **Extends the notification package** rather than creating a separate system
- **Uses the firebase package** for FCM token management with encryption
- **Follows established patterns** for controllers, services, and repositories
- **Implements proper security** using existing authentication middleware
- **Provides comprehensive error handling** with your structured error system
- **Includes monitoring and logging** for production deployment
- **Supports both unique and recurrent** notification types with rule-based eligibility

The system is production-ready and follows all your established coding conventions and architectural patterns.